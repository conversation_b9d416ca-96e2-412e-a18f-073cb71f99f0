// Internal linking utility for SEO optimization
// This utility automatically adds internal links to relevant keywords in blog content

import { getAllBlogsForLinking, Blog } from "@/lib/supabase"

interface InternalLink {
  keyword: string
  url: string
  title: string
}

// Define static internal links for attorney-related terms that should link to main pages
const STATIC_INTERNAL_LINKS: InternalLink[] = [
  {
    keyword: "car accident attorney",
    url: "/",
    title: "Find Car Accident Attorneys"
  },
  {
    keyword: "car accident lawyer",
    url: "/",
    title: "Find Car Accident Lawyers"
  },
  {
    keyword: "personal injury attorney",
    url: "/",
    title: "Personal Injury Attorneys"
  },
  {
    keyword: "personal injury lawyer",
    url: "/",
    title: "Personal Injury Lawyers"
  },
  {
    keyword: "legal representation",
    url: "/",
    title: "Legal Representation"
  },
  {
    keyword: "compensation",
    url: "/",
    title: "Compensation for Car Accidents"
  }
]

// Keywords that should link to relevant blog posts instead of static pages
const BLOG_KEYWORDS = [
  "personal injury",
  "legal advice",
  "insurance claim",
  "insurance claims",
  "settlement",
  "car accident",
  "medical malpractice",
  "slip and fall",
  "product liability",
  "statute of limitations",
  "damages",
  "negligence",
  "liability",
  "lawsuit",
  "legal process"
]

// Cache for blog posts to avoid repeated API calls
let blogPostsCache: Blog[] | null = null
let cacheTimestamp: number = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

/**
 * Get all blog posts for linking, with caching
 */
async function getBlogPostsForLinking(): Promise<Blog[]> {
  const now = Date.now()

  // Return cached data if it's still fresh
  if (blogPostsCache && (now - cacheTimestamp) < CACHE_DURATION) {
    return blogPostsCache
  }

  try {
    const { blogs } = await getAllBlogsForLinking()
    blogPostsCache = blogs
    cacheTimestamp = now
    return blogs
  } catch (error) {
    console.error("Error fetching blogs for linking:", error)
    return blogPostsCache || []
  }
}

/**
 * Find the most relevant blog post for a given keyword
 */
function findRelevantBlogPost(keyword: string, blogs: Blog[], currentSlug?: string): InternalLink | null {
  const keywordLower = keyword.toLowerCase()

  // Filter out the current blog post
  const availableBlogs = blogs.filter(blog => blog.slug !== currentSlug)

  if (availableBlogs.length === 0) return null

  // Score blogs based on keyword relevance
  const scoredBlogs = availableBlogs.map(blog => {
    let score = 0
    const titleLower = blog.title.toLowerCase()
    const contentLower = blog.content.toLowerCase()
    const tagsLower = blog.tags.map(tag => tag.toLowerCase())

    // Higher score for title matches
    if (titleLower.includes(keywordLower)) score += 10

    // Medium score for tag matches
    if (tagsLower.some(tag => tag.includes(keywordLower) || keywordLower.includes(tag))) score += 5

    // Lower score for content matches
    const contentMatches = (contentLower.match(new RegExp(keywordLower, 'g')) || []).length
    score += Math.min(contentMatches, 3) // Cap at 3 points for content matches

    return { blog, score }
  })

  // Sort by score and get the best match
  scoredBlogs.sort((a, b) => b.score - a.score)
  const bestMatch = scoredBlogs[0]

  if (bestMatch && bestMatch.score > 0) {
    return {
      keyword,
      url: `/blog/${bestMatch.blog.slug}`,
      title: bestMatch.blog.title
    }
  }

  return null
}

/**
 * Adds internal links to blog content for SEO purposes
 * @param content - The blog content to process
 * @param currentSlug - The current blog post slug to avoid self-linking
 * @returns Content with internal links added
 */
export async function addInternalLinks(content: string, currentSlug?: string): Promise<string> {
  let processedContent = content
  const usedLinks = new Set<string>() // Track which keywords we've already linked

  // Skip processing if this is a markdown header (starts with #)
  if (content.trim().startsWith('#')) {
    return content
  }

  // Skip processing if content already contains HTML links
  if (content.includes('<a ') || content.includes('</a>')) {
    return content
  }

  // Get all blog posts for dynamic linking
  const blogs = await getBlogPostsForLinking()

  // Combine static links and dynamic blog links
  const allLinks: InternalLink[] = [...STATIC_INTERNAL_LINKS]

  // Add dynamic blog links for blog keywords
  for (const keyword of BLOG_KEYWORDS) {
    const blogLink = findRelevantBlogPost(keyword, blogs, currentSlug)
    if (blogLink) {
      allLinks.push(blogLink)
    }
  }

  // Sort links by keyword length (longest first) to avoid partial matches
  const sortedLinks = allLinks.sort((a, b) => b.keyword.length - a.keyword.length)

  for (const link of sortedLinks) {
    // Skip if we've already used this keyword
    if (usedLinks.has(link.keyword.toLowerCase())) {
      continue
    }

    // Create case-insensitive regex that matches whole words only
    const regex = new RegExp(`\\b(${link.keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})\\b`, 'gi')

    // Check if the keyword exists in the content
    const matches = processedContent.match(regex)
    if (matches && matches.length > 0) {
      // Only link the first occurrence to avoid over-optimization
      let linkAdded = false
      processedContent = processedContent.replace(regex, (match) => {
        if (!linkAdded) {
          linkAdded = true
          usedLinks.add(link.keyword.toLowerCase())
          return `<a href="${link.url}" title="${link.title}" class="text-primary hover:underline font-medium">${match}</a>`
        }
        return match
      })
    }
  }

  return processedContent
}

/**
 * Processes blog content paragraphs and adds internal links
 * @param paragraphs - Array of content paragraphs
 * @param currentSlug - Current blog post slug
 * @returns Processed paragraphs with internal links
 */
export async function processContentWithLinks(paragraphs: string[], currentSlug?: string): Promise<string[]> {
  const fullContent = paragraphs.join('\n')
  const processedContent = await addInternalLinks(fullContent, currentSlug)
  return processedContent.split('\n')
}

/**
 * Safely renders HTML content with internal links
 * @param htmlContent - HTML content string
 * @returns JSX element with dangerously set innerHTML
 */
export function renderContentWithLinks(htmlContent: string) {
  return {
    __html: htmlContent
  }
}
