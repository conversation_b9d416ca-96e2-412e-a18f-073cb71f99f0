import { remark } from 'remark'
import remarkGfm from 'remark-gfm'
import remarkHtml from 'remark-html'

/**
 * Process markdown content on the server side
 * This function converts markdown to HTML
 */
export async function processMarkdownToHtml(markdownContent: string, currentSlug?: string): Promise<string> {
  try {
    // Convert markdown to HTML first
    const processedContent = await remark()
      .use(remarkGfm)
      .use(remarkHtml, { sanitize: false })
      .process(markdownContent)
    
    let htmlContent = processedContent.toString()
    
    // Then add internal links to the HTML content (safer approach)
    if (currentSlug) {
      htmlContent = await addInternalLinksToHtml(htmlContent, currentSlug)
    }
    
    return htmlContent
  } catch (error) {
    console.error('Error processing markdown:', error)
    // Fallback: return the original content with basic processing
    return markdownContent.replace(/\n/g, '<br>')
  }
}

/**
 * Add internal links to HTML content (safer than adding to markdown)
 */
async function addInternalLinksToHtml(htmlContent: string, currentSlug: string): Promise<string> {
  try {
    // Import the internal links function
    const { addInternalLinks } = await import('./internal-links')
    
    // Extract text content from HTML, add links, then merge back
    // This is a simplified approach - we'll add links to text nodes only
    let processedContent = htmlContent
    
    // Simple regex to find text content outside of HTML tags
    const textRegex = />([^<]+)</g
    let match
    const replacements: { original: string; replacement: string }[] = []
    
    while ((match = textRegex.exec(htmlContent)) !== null) {
      const textContent = match[1].trim()
      if (textContent.length > 10) { // Only process substantial text
        const linkedText = await addInternalLinks(textContent, currentSlug)
        if (linkedText !== textContent) {
          replacements.push({
            original: `>${textContent}<`,
            replacement: `>${linkedText}<`
          })
        }
      }
    }
    
    // Apply replacements
    for (const replacement of replacements) {
      processedContent = processedContent.replace(replacement.original, replacement.replacement)
    }
    
    return processedContent
  } catch (error) {
    console.error('Error adding internal links:', error)
    return htmlContent
  }
}

/**
 * Extract plain text from markdown for meta descriptions and excerpts
 */
export function extractPlainTextFromMarkdown(markdownContent: string): string {
  return markdownContent
    .replace(/#{1,6}\s+/g, '') // Remove headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold formatting
    .replace(/\*(.*?)\*/g, '$1') // Remove italic formatting
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links, keep text
    .replace(/`(.*?)`/g, '$1') // Remove inline code formatting
    .replace(/\n+/g, ' ') // Replace newlines with spaces
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim()
}

/**
 * Estimate reading time from markdown content
 */
export function estimateReadingTime(markdownContent: string): number {
  const plainText = extractPlainTextFromMarkdown(markdownContent)
  const wordsPerMinute = 200
  const wordCount = plainText.split(' ').filter(word => word.length > 0).length
  return Math.ceil(wordCount / wordsPerMinute)
}
